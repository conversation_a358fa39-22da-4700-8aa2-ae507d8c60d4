// components/invoice/InvoiceTotals.jsx
import { Colors } from '@/constants/colors';
import { Box, Separator, Text } from '@chakra-ui/react';
import { FiPlusCircle } from 'react-icons/fi';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import { currencyOptions } from '@/data/options';

export default function InvoiceTotals({
  invoiceDetails,
  discountData,
  discountDisclosure,
  handleInvoiceDetailsChange,
  selectedItems = [],
}: any) {
  // const formatCurrency = (amount: number, currencyCode: string) => {
  //   return new Intl.NumberFormat('en-US', {
  //     style: 'currency',
  //     currency: currencyCode || 'USD',
  //   }).format(amount);
  // };
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const currencyCode = org?.currency_code || 'CAD';

  const calculateIndividualTaxes = () => {
    const taxMap = new Map();

    selectedItems.forEach((item: any) => {
      if (item.taxes && Array.isArray(item.taxes)) {
        const itemSubtotal = Number(item.price) * Number(item.quantity || 1);

        item.taxes.forEach((tax: any) => {
          const taxId = tax.id;
          const taxName = tax.name || tax.tax_name || `Tax ${taxId}`;
          const taxRate = Number(tax.value || tax.tax_value || tax.rate) || 0;
          const taxAmount = (taxRate / 100) * itemSubtotal;

          if (taxMap.has(taxId)) {
            const existingTax = taxMap.get(taxId);
            existingTax.amount += taxAmount;
          } else {
            taxMap.set(taxId, {
              id: taxId,
              name: taxName,
              rate: taxRate,
              amount: taxAmount,
            });
          }
        });
      }
    });

    return Array.from(taxMap.values()).filter((tax) => tax.amount > 0);
  };

  const individualTaxes = calculateIndividualTaxes();

  return (
    <Box px={6}>
      <Box
        display={'flex'}
        flexDirection={'column'}
        w={'full'}
        justifyContent={'flex-end'}
        alignItems={'end'}
      >
        {/* Subtotal */}
        <Box display={'flex'} alignItems={'center'} gap={'2rem'} pb={4}>
          <Box w={{ base: '100%', md: '300px' }}>
            <Text textAlign={'right'} color="gray.600">
              Subtotal
            </Text>
          </Box>
          <Box display={'flex'} justifyContent={'flex-end'} w={'150px'}>
            {' '}
            <Text textAlign={'left'}>
              {formatMoney(Number(invoiceDetails?.invoiceSubTotal || 0), {
                currencyCode: invoiceDetails?.currency_code,
              })}
            </Text>
          </Box>
        </Box>

        {/* Discount */}
        <Box display={'flex'} alignItems={'center'} gap={'2rem'} pb={4}>
          <Box
            display={'flex'}
            justifyContent={'flex-end'}
            gap={1}
            textAlign={'right'}
            alignItems={'center'}
            color={Colors?.ORANGE?.PRIMARY}
            cursor={'pointer'}
            onClick={discountDisclosure.onOpen}
            w={{ base: '100%', md: '300px' }}
          >
            <FiPlusCircle size="12px" />
            <Text textAlign={'left'} fontWeight={'semibold'}>
              {discountData.value > 0 ? 'Edit discount' : 'Add a discount'}
            </Text>
          </Box>
          <Box display={'flex'} justifyContent={'flex-end'} w={'150px'}>
            <Text>
              {discountData.value > 0
                ? discountData.type === 'percentage'
                  ? `-${discountData.value}% (${formatMoney(
                      Number(invoiceDetails?.discount || 0),
                      { currencyCode: invoiceDetails?.currency_code }
                    )})`
                  : `-${formatMoney(Number(invoiceDetails?.discount || 0), {
                      currencyCode: invoiceDetails?.currency_code,
                    })}`
                : null}
            </Text>
          </Box>
        </Box>

        {/* Individual Taxes */}
        {individualTaxes.length > 0 && (
          <>
            {individualTaxes.map((tax: any) => (
              <Box
                key={tax.id}
                display={'flex'}
                alignItems={'center'}
                gap={'2rem'}
                pb={4}
              >
                <Box w={{ base: '100%', md: '300px' }}>
                  <Text textAlign={'right'} color="gray.600">
                    {tax.name} ({tax.rate}%)
                  </Text>
                </Box>
                <Box display={'flex'} justifyContent={'flex-end'} w={'150px'}>
                  <Text fontWeight="medium" fontSize="sm">
                    {formatMoney(tax.amount, {
                      currencyCode: invoiceDetails?.currency_code,
                    })}
                  </Text>
                </Box>
              </Box>
            ))}
          </>
        )}

        {/* Total */}
        <Box display={'flex'} alignItems={'center'} pb={4}>
          <Box
            display={{ base: 'block', md: 'flex' }}
            alignItems={'center'}
            w={{ base: '100%', md: '300px' }}
            gap={{ base: 0, md: 4 }}
          >
            <Text textAlign={'right'} fontWeight="bold" fontSize="md">
              Total
            </Text>
            {/* Currency code */}
            <Box
              // minW={60}
              // maxW={60}
              // w={60}
              // pl={'5'}
              display={'flex'}
              justifyContent={'flex-end'}
            >
              <CustomSelect
                placeholder="Select Currency"
                onChange={(val) =>
                  handleInvoiceDetailsChange('currency_code', val.value)
                }
                options={currencyOptions}
                defaultValue={currencyOptions?.find(
                  (option) => option.value === currencyCode
                )}
              />
            </Box>
          </Box>
          <Box display={'flex'} justifyContent={'flex-end'} w={'150px'}>
            <Text fontWeight="bold" fontSize="md">
              {formatMoney(Number(invoiceDetails?.total || 0), {
                currencyCode: invoiceDetails?.currency_code,
              })}
            </Text>
          </Box>
        </Box>

        <Box
          borderTop={'1px solid #E2E8F0'}
          display={'flex'}
          justifyContent={'flex-end'}
          gap={'2rem'}
          pb={4}
          w={'full'}
          pt={2}
        >
          <Box w={'300px'}>
            <Text textAlign={'right'} fontWeight="bold" fontSize="md">
              Amount Due
            </Text>
          </Box>
          <Box display={'flex'} justifyContent={'flex-end'} w={'150px'}>
            <Text
              fontWeight="bold"
              fontSize="md"
              color={Colors?.ORANGE?.PRIMARY}
            >
              {formatMoney(Number(invoiceDetails?.amountDue), {
                currencyCode: invoiceDetails?.currency_code,
              })}
              {/* {formatCurrency(Number(invoiceDetails?.amountDue || 0), 'USD')} */}
            </Text>
          </Box>
        </Box>
      </Box>

      <Separator my={2} borderColor="#636D79" />
      <Box my={'4rem'}>
        <Text
          whiteSpace={'nowrap'}
          color={'gray.600'}
          fontWeight="medium"
          minW="8rem"
        >
          Notes / Terms
        </Text>
        <CustomTextArea
          inputProps={{
            width: '50%',
            placeholder: 'Start typing here...',
            value: invoiceDetails?.memo,
            onChange: (e: any) =>
              handleInvoiceDetailsChange('memo', e.target.value),
            borderRadius: '0',
            minH: '80px',
            resize: 'vertical',
          }}
        />
      </Box>
    </Box>
  );
}
